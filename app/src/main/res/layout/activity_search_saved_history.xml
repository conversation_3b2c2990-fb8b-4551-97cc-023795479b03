<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
		xmlns:app="http://schemas.android.com/apk/res-auto"
		xmlns:tools="http://schemas.android.com/tools"
		android:layout_width="match_parent"
		android:layout_height="match_parent"
		android:background="@drawable/bg_new_skin_activity_container"
		tools:context="id.co.bri.brimo.ui.activities.dompetdigitalreskin.SearchSavedHistoryDompetDigitalActivity">

	<!-- Toolbar -->
	<include
			android:id="@+id/toolbar"
			layout="@layout/toolbar_new_skin"
			android:layout_width="match_parent"
			android:layout_height="wrap_content"
			/>

	<androidx.coordinatorlayout.widget.CoordinatorLayout
			android:id="@+id/content"
			android:layout_width="match_parent"
			android:layout_height="match_parent">

		<ScrollView
				android:layout_width="match_parent"
				android:layout_height="match_parent"
				android:layout_marginTop="70dp"
				android:paddingHorizontal="16dp"
				android:orientation="vertical"
				android:paddingBottom="100dp"
				android:background="@drawable/bg_card_rounded_ns"
				android:clipToPadding="false">

			<LinearLayout
					android:layout_width="match_parent"
					android:layout_height="wrap_content"
					android:orientation="vertical"
					android:paddingTop="16dp">

				<!-- Tab Section for Favorit and Riwayat -->
				<LinearLayout
						android:layout_width="match_parent"
						android:layout_height="wrap_content"
						android:orientation="vertical">

					<!-- Search bar -->
					<androidx.appcompat.widget.SearchView
							android:id="@+id/search_view"
							android:layout_width="match_parent"
							android:layout_height="40dp"
							android:layout_marginBottom="24dp"
							android:background="@drawable/bg_input_black_100_brimo_ns"
							app:iconifiedByDefault="false"
							app:searchIcon="@drawable/ic_search_new_skin_20"
							android:theme="@style/AppSearchViewSmall"
							app:queryBackground="@color/transparent"
							app:closeIcon="@drawable/ic_clear_ns"
							app:queryHint="@string/cari_pelanggan_atau_layanan"
							/>

					<!-- No Saved Data Message -->
					<LinearLayout
							android:id="@+id/ll_start_search"
							android:layout_width="match_parent"
							android:layout_height="wrap_content"
							android:orientation="vertical"
							android:gravity="center"
							android:padding="32dp"
							android:visibility="visible">

						<ImageView
								android:layout_width="120dp"
								android:layout_height="120dp"
								android:src="@drawable/ic_search_start"
								android:layout_marginBottom="16dp" />

						<TextView
								android:layout_width="wrap_content"
								android:layout_height="wrap_content"
								android:text="@string/txt_cari_tujuan_transaksi"
								android:textStyle="bold"
								android:textColor="@color/black"
								android:layout_marginTop="8dp" />

						<TextView
								android:layout_width="wrap_content"
								android:layout_height="wrap_content"
								android:text="@string/txt_cari_favorit_dan_riwayat"
								android:textSize="12sp"
								android:gravity="center"
								android:layout_marginTop="4dp" />
					</LinearLayout>

					<!-- No Saved Data Message -->
					<LinearLayout
							android:id="@+id/ll_no_data_search_found"
							android:layout_width="match_parent"
							android:layout_height="wrap_content"
							android:orientation="vertical"
							android:gravity="center"
							android:padding="32dp"
							android:visibility="visible">

						<ImageView
								android:layout_width="120dp"
								android:layout_height="120dp"
								android:src="@drawable/empty_box_3d"
								android:layout_marginBottom="16dp" />

						<TextView
								android:layout_width="wrap_content"
								android:layout_height="wrap_content"
								android:text="@string/result_found"
								android:textStyle="bold"
								android:textColor="@color/black"
								android:layout_marginTop="8dp" />

						<TextView
								android:layout_width="wrap_content"
								android:layout_height="wrap_content"
								android:text="@string/no_data_found_desc_other"
								android:textSize="12sp"
								android:gravity="center"
								android:layout_marginTop="4dp" />
					</LinearLayout>

					<!-- Favorit Content (Saved List) -->
					<LinearLayout
							android:id="@+id/content_favorit"
							android:layout_width="match_parent"
							android:layout_height="wrap_content"
							android:orientation="vertical"
							android:layout_marginBottom="24dp"
							android:visibility="visible">

						<TextView
								android:layout_width="match_parent"
								android:layout_height="wrap_content"
								android:text="@string/favorit"
								android:textSize="14sp"
								android:textColor="@color/text_black_default_ns"
								android:textStyle="bold"
								android:layout_marginBottom="16dp"/>

						<!-- Saved List RecyclerView -->
						<androidx.recyclerview.widget.RecyclerView
								android:id="@+id/rv_daftar_favorit"
								android:layout_width="match_parent"
								android:layout_height="wrap_content"
								android:layoutAnimation="@anim/layout_animation_fade_in"
								android:visibility="visible"
								/>

					</LinearLayout>

					<!-- Riwayat Content (History) -->
					<LinearLayout
							android:id="@+id/content_riwayat"
							android:layout_width="match_parent"
							android:layout_height="wrap_content"
							android:orientation="vertical"
							android:layout_marginBottom="24dp"
							android:visibility="visible">

						<TextView
								android:layout_width="match_parent"
								android:layout_height="wrap_content"
								android:text="@string/txt_riwayat"
								android:textSize="14sp"
								android:textColor="@color/text_black_default_ns"
								android:textStyle="bold"
								android:layout_marginBottom="16dp"/>

						<!-- History RecyclerView -->
						<androidx.recyclerview.widget.RecyclerView
								android:id="@+id/rv_riwayat"
								android:layout_width="match_parent"
								android:layout_height="wrap_content"
								android:layoutAnimation="@anim/layout_animation_fade_in"
								android:visibility="visible" />
					</LinearLayout>
				</LinearLayout>
			</LinearLayout>
		</ScrollView>
	</androidx.coordinatorlayout.widget.CoordinatorLayout>


</FrameLayout>