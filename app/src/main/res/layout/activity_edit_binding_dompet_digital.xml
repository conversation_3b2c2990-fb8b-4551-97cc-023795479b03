<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
		android:layout_width="match_parent"
		android:layout_height="match_parent"
		android:background="@drawable/bg_new_skin_activity_container"
		>

	<!-- Toolbar -->
	<include
			android:id="@+id/toolbar"
			layout="@layout/toolbar_new_skin"
			android:layout_width="match_parent"
			android:layout_height="wrap_content" />

	<androidx.coordinatorlayout.widget.CoordinatorLayout
			android:id="@+id/content"
			android:layout_width="match_parent"
			android:layout_height="match_parent">

		<ScrollView
				android:layout_width="match_parent"
				android:layout_height="match_parent"
				android:layout_marginTop="70dp"
				android:paddingHorizontal="16dp"
				android:orientation="vertical"
				android:paddingBottom="100dp"
				android:background="@drawable/bg_card_rounded_ns"
				android:clipToPadding="false">

			<LinearLayout
					android:layout_width="match_parent"
					android:layout_height="wrap_content"
					android:orientation="vertical"
					android:paddingTop="16dp">

				<LinearLayout
						android:layout_width="match_parent"
						android:layout_height="wrap_content"
						android:orientation="vertical">

						<!-- Binding Wallet List RecyclerView -->
						<androidx.recyclerview.widget.RecyclerView
								android:id="@+id/rv_daftar_favorit"
								android:layout_width="match_parent"
								android:layout_height="wrap_content"
								android:layoutAnimation="@anim/layout_animation_fade_in"
								android:visibility="visible"
								/>

				</LinearLayout>
			</LinearLayout>
		</ScrollView>
	</androidx.coordinatorlayout.widget.CoordinatorLayout>

	<!-- Bottom Button -->
	<LinearLayout
			android:layout_width="match_parent"
			android:layout_height="wrap_content"
			android:layout_gravity="bottom"
			android:layout_alignParentBottom="true"
			android:orientation="vertical"
			android:padding="16dp"
			android:elevation="8dp"
			android:background="@android:color/white">

		<Button
				android:id="@+id/btnSubmit"
				android:layout_width="match_parent"
				android:layout_height="56dp"
				style="@style/CustomButtonStyle"
				android:text="@string/txt_hubungkan_wallet"
				android:textSize="16sp"
				android:textAllCaps="false"
				android:background="@drawable/rounded_button_ns"
				android:textColor="@color/selector_text_color_button_primary_ns"
				/>
	</LinearLayout>
</FrameLayout>