package id.co.bri.brimo.contract.IPresenter.bukaValas;

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.models.apimodel.request.LocationRequest;
import id.co.bri.brimo.models.apimodel.request.SearchLocationRequest;

public interface IPilihKantorBukaValasPresenter<V extends IMvpView>
        extends IMvpPresenter<V> {

    void sendLokasiSendiri(LocationRequest locationRequest);

    void sendSearchLocation(SearchLocationRequest searchLocationRequest);

    void setUrl(String url);

    void setSearchUrl(String searchUrl);
}
