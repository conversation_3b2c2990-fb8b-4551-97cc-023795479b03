package id.co.bri.brimo.contract.IPresenter.lifestyle.ekspedisi

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView

interface IWebviewEkspedisiPresenter<V : IMvpView?> : IMvpPresenter<V> {

    fun setUrlRevokeSession(urlRevokeSession: String)

    fun setUrlConfirmationKirimBarang(urlConfirm: String)

    fun setUrlPaymentKirimBarang(urlPayment: String)

    fun getRevokeSession(sessionId: String, isInquiry: Boolean)

    fun getConfirmationKirimBarang(orderId: String, billNumber: String)

}