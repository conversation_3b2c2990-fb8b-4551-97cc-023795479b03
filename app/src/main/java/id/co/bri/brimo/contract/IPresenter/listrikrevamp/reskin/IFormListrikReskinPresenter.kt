package id.co.bri.brimo.contract.IPresenter.listrikrevamp.reskin

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IPresenter.base.InquiryConfirmation
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.InquiryPlnRequest
import id.co.bri.brimo.models.apimodel.request.revampbriva.PayBrivaRevampRequest

import com.google.gson.annotations.SerializedName
import id.co.bri.brimo.contract.IPresenter.base.IBaseTransactionPresenter
import id.co.bri.brimo.models.apimodel.request.StatusRequest

interface IFormListrikReskinPresenter<V>: IBaseTransactionPresenter<V> where V : IMvpView {
    fun getDataForm()

    fun postInquiry(request: InquiryPlnRequest)

    fun getDataConfirmation(param: InquiryConfirmation)

    fun payment(param: PayBrivaRevampRequest)

    fun cetakToken()

    fun detailToken(data: StatusRequest)

    fun addSavedList(param: SavedListNs, saveAs: String)

    fun updateSavedList(param: SavedListNs)

    fun removeSavedList(param: SavedListNs)

    fun favoriteSavedList(param: SavedListNs)

    fun unfavoriteSavedList(param: SavedListNs)
}

data class SavedListNs (
    @SerializedName("reference_number") var referenceNumber: String?= null,
    @SerializedName("saved_id") var savedId: Int?= null,
    @SerializedName("product_id") var productId: String?= null,
    @SerializedName("save_as") var saveAs: String?= null,
    @SerializedName("pln_type_code") var plnTypeCode: String?= null,
    @SerializedName("payment_number") var paymentNumber: String?= null,
)