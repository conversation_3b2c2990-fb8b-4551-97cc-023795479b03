package id.co.bri.brimo.contract.IPresenter.bukaValas;

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.models.apimodel.response.GeneralConfirmationResponse;
import id.co.bri.brimo.models.apimodel.response.PendingResponse;
import id.co.bri.brimo.models.daomodel.Transaksi;

public interface IPendingBukaValasPresenter<V extends IMvpView>
        extends IMvpPresenter<V> {

    void getDataPaid(PendingResponse pendingResponse, String mUrl, GeneralConfirmationResponse generalConfirmationResponse);

    void getDataPaidFastMenu(PendingResponse pendingResponse, String mUrl, GeneralConfirmationResponse generalConfirmationResponse);

    void onSaveTransaksiPfm(Transaksi transaksi);

    Transaksi generateTransaksiModel(int kategoriId, long amount, String referenceNumber, String billingName, String trx);

    boolean onLoadGetReceipt();
}
