package id.co.bri.brimo.contract.IPresenter.dashboard;

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.models.apimodel.request.InquiryWalletRequest;
import id.co.bri.brimo.models.apimodel.request.splitbill.NotifikasiSplitBillRequest;
import id.co.bri.brimo.models.apimodel.response.lifestyle.ekspedisi.NotifikasiMokirimRequest;
import id.co.bri.brimo.models.daomodel.RateUs;
import id.co.bri.brimo.models.NotifikasiModel;

public interface IDashboardActivityPresenter<V extends IMvpView>
        extends IMvpPresenter<V> {

    void logOut();

    void setUrlLogout(String urlLogout);

    void setUrlRateData(String urlRateData);

    void onRateData();

    void onSaveRateDevice(RateUs rateUs);

    void CheckRate();

    void onUpdateRateCounter(long rateCounter, long id);

    void getDataInquiry(String reqSeq,String typeInquiry,String urlKonfirmasi,String urlPayment, String title, String typeInquiryRevamp);

    void setInquiryUrl(String url);

    void getDataProduct(String promoId, String blastId, String titleButton, NotifikasiModel notifikasiModel);

    void setPromoUrl(String url);

    void getTartunNds();

    void getDataNotif(String reqContent,String urlData, String typeNotif);

    void getDataDashboardLifestyleMenu(String url);

    void getConfirmationMokirim(NotifikasiMokirimRequest notifikasiMokirimRequest, String urlPaymentMokirim);

    // untuk sementara di komen menunggu konfirmasi fitur ini release kapan
//    void setUrlGetAktivasiVoiceAssistant(String url);

//    void getAktivasiVoiceAssistant();

    void getConfirmationSplitBill(NotifikasiSplitBillRequest notifikasiSplitBillRequest, String urlPayment);

}
