package id.co.bri.brimo.contract.IPresenter.transfer;

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.models.apimodel.request.InquiryTransferAliasRevampRequest;
import id.co.bri.brimo.models.apimodel.request.revamptransfer.ConfirmationTransferRtgsRevampRequest;
import id.co.bri.brimo.models.apimodel.request.smarttransfer.RecommendAccountSmartTransferRequest;

public interface IInquiryTransferRevampPresenter<V extends IMvpView>
        extends IMvpPresenter<V> {

    void getDataKonfirmasi(String refNumb, String account, String ammount, String favName, boolean isFromFastMenu, String transferMethod, String note);
    void getDataKonfirmasiRtgs(ConfirmationTransferRtgsRevampRequest confirmationTransferRtgsRevampRequest, boolean isFromFastMenu);
    void getListCity(String swiftCode, boolean isFromFastMenu);
    void setUrlKonfirmasi(String urlKonfirmasi);
    void setUrlListCity(String urlListCity);
    String getSaldoRekeningUtama();
    String getAccountDefault();
    void getDataKonfirmasiAft(String refNumb, String account, String amount, String transferMethod, String note, String frequency, String aftDateStart, String aftDateEnd, String saveAs);
    void setUrlKonfirmasiAft(String urlKonfirmasi);
    void getEditKonfirmasiAft(String refNumb, String account, String amount, String transferMethod, String note, String frequency, String aftDateStart, String aftDateEnd, String idAft);
    void setUrlKonfirmasiEditAft(String urlKonfirmasiEdit);
    boolean getPrefIsFirstTimeAft();
    void setPrefIsFirstTimeAft(boolean isFirstTime);
}
