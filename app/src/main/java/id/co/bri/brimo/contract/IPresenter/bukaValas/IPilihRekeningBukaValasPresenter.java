package id.co.bri.brimo.contract.IPresenter.bukaValas;

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;

public interface IPilihRekeningBukaValasPresenter<V extends IMvpView>
        extends IMvpPresenter<V> {

    void getData();

//    void getInquiry(InquiryVallasRequest request);

    void setFormUrl(String urlConfirmation);

    void setKonfirmasiUrl(String konfirmasiUrl);

    void setPaymentUrl(String paymentUrl);
}
