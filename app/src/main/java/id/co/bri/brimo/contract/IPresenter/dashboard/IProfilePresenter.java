package id.co.bri.brimo.contract.IPresenter.dashboard;

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.models.apimodel.request.RevokeSessionRequest;

public interface IProfilePresenter<V extends IMvpView>
        extends IMvpPresenter<V> {

    void onGetDetailAkun();

    void setFormAkunUrl(String formAkunUrl);


    void setBioChangedDialogShown(Boolean statusBioChangeShown);

    void setLogoutUrl(String logoutUrl);

    void logOut();

    void setUrlProfilBripoint(String urlProfilBripoint);

    void onGetProfilBripoint();

    void setUrlBripoint(String urlBripoint);

    void onGetBripoint();

    void setUrlChatBanking(String urlChatBanking);

    void onGetChatBanking();

    String onGetBiometricType();

    String getValueKeyBiometric();
    Boolean getBioChanged();

    Boolean getStatusAktivasi();

    void getVoip();

    void setUrlVoip(String url);

    void setUrlRevoke(String urlRevoke);

    void revokeSession(RevokeSessionRequest request);

    void getInitiateResource();

    void setUrlEnrollBiometric(String urlBio);

    void setUrlRemoveBiometric(String urlRemove);

    void updateStatusAktivasi(boolean statusAktivasi);

    void getRemoveBiometric(String pin, String checkSum);

    void getDataBiometric(String pin, String checkSum);

    void updateBioChanged(Boolean bioChanged);

    void updateStatusUpdateBio(Boolean statusUpdate);

    void setUrlBripoinCoupon(String url);

    void getBripoinCoupon();

    void setUrlPrefrences(String urlPrefrences);

    void updatePrefrencesLanguage(String id);

    void setUrlTentangBrimo(String urlTentangBrimo);

    void getDataTentangBrimo();


// untuk sementara di komen menunggu konfirmasi fitur ini release kapan
//    void setUrlUpdateAktivasiVoiceAssistant(String url);
//
//    Boolean getAktivasiVoiceAssistant();
//
//    void updateAktivasiVoiceAssistant(Boolean newStatus, String pin);

    void setUrlCheckSmartTransfer(String urlCheckUserConsent);

    void checkSmartTransfer();

    void setUrlSmartTransferManageUserConsent(String urlManageUserConsent);

    void smartTransferManageUserConsent(Boolean status);
}