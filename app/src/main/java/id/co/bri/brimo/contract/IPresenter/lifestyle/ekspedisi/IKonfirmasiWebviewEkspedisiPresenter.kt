package id.co.bri.brimo.contract.IPresenter.lifestyle.ekspedisi

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.response.lifestyle.ekspedisi.KonfirmasiWebviewEkspedisiResponse

interface IKonfirmasiWebviewEkspedisiPresenter <V : IMvpView> : IMvpPresenter<V> {

    fun getDefaultSaldo()

    fun setUrlPayment(url: String)

    fun getDataPaymentRevamp(pin: String, konfirmasiWebviewEkspedisiResponse: KonfirmasiWebviewEkspedisiResponse,
                             account: String, saveAs: String, note: String, fromFastMenu: Boolean)

}