package id.co.bri.brimo.contract.IPresenter.bukaValas;

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.models.apimodel.request.KonfimasiBukaValasRequest;

public interface IInquiryBukaValasPresenter<V extends IMvpView>
        extends IMvpPresenter<V> {

    void getDataKonfirmasi(KonfimasiBukaValasRequest request);

    void setUrlConfirmation(String konfirmasiUrl);
}
