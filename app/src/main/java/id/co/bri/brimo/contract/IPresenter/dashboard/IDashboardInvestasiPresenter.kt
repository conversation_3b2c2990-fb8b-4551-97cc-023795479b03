package id.co.bri.brimo.contract.IPresenter.dashboard

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.rdn.InquiryRdnRequest
import id.co.bri.brimo.models.apimodel.response.esbn.DashboardDataSbnResponse


interface IDashboardInvestasiPresenter<V : IMvpView> : IMvpPresenter<V> {

    fun setUrlInvestasi(url : String)

    fun setUrlInvestasiForced(url : String)

    fun setUrlInvestasiFaq(url: String)

    fun setUrlInvestasiPromo(url: String)

    fun setUrlInvestasiContent(url: String)

    fun setUrlInvestasiRecomendation(url: String)

    fun setUrlInvestasiMenu(url: String)

    fun setUrlBukaRdn(url: String)

    fun setUrlDepositoRegis(url: String)

    fun seturlDplkRegis(url : String)

    fun setUrlGetListAccountRdn(url : String)

    fun setUrlInquiryRdn(url : String)

    fun setUrlValidateUsers(urlValidate : String)

    fun setUrlKemenkeu(urlKemenkeu : String)

    fun setUrlSbnDashboardHeader(urlDashboardHeader: String)

    fun setUrlBeliSbn(urlBeliSbn : String)

    fun setUrlInvesmentRecap(urlInvesmentRecap : String)

    fun getDataInvestasiFaq()

    fun getDataInvestasiPromo()

    fun getDataInvestasiContent()

    fun getDataInvestasiRecommedantion()

    fun getDataInvestasiMenu()

    fun getDataInvestasiAsset()

    fun getDataInvestasiAssetForce()

    fun onGetOnBoarding()

    fun getDepositoRegis()

    fun getDplkRegis()

    fun getListAccountRdn()

    fun getTopUpRdn(request : InquiryRdnRequest)

    fun getSbnDashboardHeader()

    fun getSbnBeliSbn()
    fun getInvesmentRecap()
}