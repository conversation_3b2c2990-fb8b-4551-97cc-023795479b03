package id.co.bri.brimo.contract.IPresenter.transfer;

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.models.daomodel.Transaksi;

public interface IKonfirmasiTransferPresenter<V extends IMvpView>
        extends IMvpPresenter<V> {

    void getPaidTransfer(
            String refNumb,
            String pin,
            String pfmCategory,
            boolean immediately,
            int amount,
            String bankDestination,
            String accountDestination,
            String note);

    void saveTransaksiPfm(Transaksi transaksi);

    Transaksi generateTransaksiModel(String kategoriId, int amount, String refnum, String bankDestination, String bankName);

}
