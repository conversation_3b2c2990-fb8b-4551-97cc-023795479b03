package id.co.bri.brimo.contract.IPresenter.bukaValas;

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.models.apimodel.response.GeneralConfirmationResponse;
import id.co.bri.brimo.models.apimodel.response.KonfirmasiBukaValasResponse;
import id.co.bri.brimo.models.daomodel.Transaksi;

public interface IKonfirmasiBukaValasPresenter<V extends IMvpView>
        extends IMvpPresenter<V> {
    void getDataPayment (String pin, String note, KonfirmasiBukaValasResponse response, boolean fromFast);

    void setUrlPayment(String urlPayment);

    Transaksi generateTransaksiModel(int kategoriId, long amount, String referenceNumber, String billingName);

    void onSaveTransaksiPfm(Transaksi transaksi);
}
