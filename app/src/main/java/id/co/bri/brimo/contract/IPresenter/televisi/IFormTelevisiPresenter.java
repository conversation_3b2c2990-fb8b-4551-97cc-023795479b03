package id.co.bri.brimo.contract.IPresenter.televisi;

import id.co.bri.brimo.contract.IPresenter.base.IBaseFormPresenter;
import id.co.bri.brimo.contract.IView.base.IBaseFormView;
import id.co.bri.brimo.models.apimodel.request.InquiryTelevisiRequest;


public interface IFormTelevisiPresenter <V extends IBaseFormView>
        extends IBaseFormPresenter<V> {

    void getDataInquiry(InquiryTelevisiRequest request, boolean isFromFast);
}
