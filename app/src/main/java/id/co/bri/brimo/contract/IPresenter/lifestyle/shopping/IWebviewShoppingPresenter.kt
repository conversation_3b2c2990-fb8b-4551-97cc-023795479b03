package id.co.bri.brimo.contract.IPresenter.lifestyle.shopping

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.response.InquiryBrivaRevampResponse

interface IWebviewShoppingPresenter <V : IMvpView> : IMvpPresenter<V> {

    fun getRevokeSession(sessionId: String, isInquiry: Boolean)

    fun setUrlRevokeSession(urlRevokeSession: String)

    fun setUrlConfirmationMobelanja(urlConfirmationMobelanja: String)

    fun getConfirmationMobelanja(orderNumber: String)

    fun setUrlPaymentMobelanja(urlPaymentMobelanja: String)
    fun postPauseTime(time: Long)

    fun fetchPauseTime(): Long

}