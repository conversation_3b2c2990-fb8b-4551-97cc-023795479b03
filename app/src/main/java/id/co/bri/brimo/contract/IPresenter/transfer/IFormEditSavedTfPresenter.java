package id.co.bri.brimo.contract.IPresenter.transfer;

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.models.apimodel.request.revamptransfer.SavedDataRequest;

public interface IFormEditSavedTfPresenter<V extends IMvpView>
        extends IMvpPresenter<V> {

    void setUpdateTitle(String url, int position, String saveTitle, String saveId, String paymentType, String tfMethod);

    void setUpdateTitleBRIVA(String url, int position, String saveTitle, String saveId, String subtitle,String mJourneyType);

    void setUpdateTitleEmas(String url, int position, String saveTitle, String saveId);

    void saveSavedData(String urlSave, SavedDataRequest savedDataRequest);

    void setUpdateTitleDplk(String url, int position, String saveTitle, String saveId);

}
