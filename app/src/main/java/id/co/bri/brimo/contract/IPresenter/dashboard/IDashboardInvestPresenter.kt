package id.co.bri.brimo.contract.IPresenter.dashboard

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView

interface IDashboardInvestPresenter <V : IMvpView> : IMvpPresenter<V> {

    fun setUrlInvestasiFaq(url: String)

    fun setUrlInvestasiPromo(url: String)

    fun setUrlInvestasiContent(url: String)

    fun setUrlInvestasiRecomendation(url: String)

    fun setUrlInvestasiMenu(url: String)

    fun getDataInvestasiFaq()

    fun getDataInvestasiPromo()

    fun getDataInvestasiContent()

    fun getDataInvestasiRecommedantion()

    fun getDataInvestasiMenu()
}