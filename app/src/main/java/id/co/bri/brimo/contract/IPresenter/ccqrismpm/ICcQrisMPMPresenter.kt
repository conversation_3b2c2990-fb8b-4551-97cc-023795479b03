package id.co.bri.brimo.contract.IPresenter.ccqrismpm

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.ccqrismpm.SetDefaultQrPaymentRequest
import id.co.bri.brimo.models.apimodel.response.ccqrismpm.CcQrisMpmResponse

interface ICcQrisMPMPresenter<V : IMvpView> : IMvpPresenter<V> {
    fun setUrlListSof(url: String)
    fun setUrlChangeSof(url: String)
    fun getDataListSof()
    fun setDefaultSof(createDataRequest: SetDefaultQrPaymentRequest)
    fun getListBalanceSavings(accountList: ArrayList<CcQrisMpmResponse.AccountList>)
    fun getListBalanceCc(ccList: ArrayList<CcQrisMpmResponse.CcList>)
    fun setUrlGetBalanceCc(url: String)
    fun setUrlTerm(url:String)
    fun getDataTerm()

}