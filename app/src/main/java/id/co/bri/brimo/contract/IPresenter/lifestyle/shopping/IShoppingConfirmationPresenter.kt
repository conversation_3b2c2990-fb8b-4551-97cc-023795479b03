package id.co.bri.brimo.contract.IPresenter.lifestyle.shopping

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.response.InquiryBrivaRevampResponse

interface IShoppingConfirmationPresenter <V : IMvpView> : IMvpPresenter<V> {

    fun getDefaultSaldo()

    fun setUrlPayment(url: String)

    fun getDataPaymentRevamp(pin: String, mInquiryBrivaRevampResponse: InquiryBrivaRevampResponse,
                             account: String, saveAs: String, note: String, fromFastMenu: Boolean)

}