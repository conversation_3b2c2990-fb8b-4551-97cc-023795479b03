package id.co.bri.brimo.contract.IPresenter.dashboard;

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.models.apimodel.request.InquiryWalletRequest;
import id.co.bri.brimo.models.apimodel.request.dompetdigitalrevamp.InquiryDompetDigitalRequest;
import id.co.bri.brimo.models.apimodel.response.EwalletProductListResponse;
import id.co.bri.brimo.models.apimodel.request.ResendSmsRequest;

public interface IDasboardIBPresenter<V extends IMvpView>
        extends IMvpPresenter<V> {

    void onCloseButtonClicked();

    void getOnboarding();

    void getSaldo();

    void updateTokenFirebase();

    void getMenuDefault();

    void getPromoFeatured();

    void getKategoriItem(String namaPromo);

    void getDetailItem(String id);

    void getLatestBlastNotif();

    void getNotificationUnreads();

    void getReadNotifFastMenu(String blastId);

    void getDataIbbiz();

    void getDataMagicLinkRegis();

    void getDataOtpEmailRegis();

    void getPusatBantuanSafety(String id);

    ResendSmsRequest sendRequestCheckPoinRegis();

    // set URL
    void setUrlSaldo(String urlSaldo);

    void setFormUrl(String url);

    void setSubscribeUrl(String subscribeUrl);

    void setKategoriItemUrl(String kategoriItemUrl);

    void setDetailItemUrl(String detailItemUrl);

    void setLatestBlastNotifUrl(String latestBlastUrl);

    void setNotificationUnreads(String notifUnreadUrl);

    void setUrlReadNotif(String urlRead);

    void setUrlIbbiz(String urlIbbiz);

    void setUrlInfoSaldoHold(String urlInfoSaldoHold);

    void setSafetyMode(String urlSafety);

    void setUrlPFMSummary(String url);

    void setUrlPusatBantuanSafety(String urlPusatBantuanSafety);

    void getDataInquiryDompetDigital(InquiryDompetDigitalRequest request);

    void setInquiryUrlDompetDigital(String url);

    void getEwalletBindingList();

    void getEwalletBalance(EwalletProductListResponse ewalletProductListResponse);

    void getTimerSafetyMode();

    void setUnbindingEwallet(String type);

    void setUrlEwalletBindingList(String url);

    void setUrlEwalletBalance(String url);

    void setUrlEwalletUnBinding(String url);

    void setUrlMagicLinkRegis(String urlMagicLinkRegis);

    void setUrlOtpEmailRegis(String urlOtpEmailRegis);

    void getInfoSaldoHold();

    void getBrimoPrefSaldoHold();

    void saveBrimoPrefSaldoHold();

    void getTotalAmount();

    void getTermCondition();

    void getBubbleShowNewOnboarding();

    void updateIsNewFalse(int menuId, int isNewFlag);

    void updateKategori(int kategoriId);

}