package id.co.bri.brimo.contract.IPresenter.televisi;

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.models.apimodel.request.InquiryTelevisiRequest;
import id.co.bri.brimo.models.apimodel.request.televisiRevamp.AddSavedListTelevisiRequest;

public interface ITambahTelevisiPresenter<V extends IMvpView>
        extends IMvpPresenter<V> {

    void getDataInquiry(InquiryTelevisiRequest request);

    void getDataInquiryAddSavedList(AddSavedListTelevisiRequest addSavedListTelevisiRequest);

    void setInquiryUrl(String url);
}
